<template>
  <div>
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Store API Keys</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <div class="card-footer">
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                >Export<i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="storesTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th width="8%">Merchant ID</th>
                        <th width="8%">Store ID</th>
                        <th width="11%">Retailer</th>
                        <th width="11%">Integrator ID</th>
                        <th width="12%">CanPay Internal Version</th>
                        <th width="11%">APP Key</th>
                        <th width="10%">API Secret</th>
                        <th width="5%">Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- API Secret Edit Modal Start -->
    <b-modal
      id="editModal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-3">
            <label>Merchant ID : </label>
          </div>
          <div class="col-md-3"><span>{{storeDetails.merchant_id}}</span>
          </div>
          <div class="col-md-3">
            <label>Store ID : </label>
          </div>
          <div class="col-md-3"><span>{{storeDetails.store_id}}</span>
          </div>
          <div class="col-md-3">
            <label>Retailer : </label>
          </div>
          <div class="col-md-3"><span>{{storeDetails.retailer}}</span>
          </div><div class="col-md-3">
            <label>APP Key : </label>
          </div>
          <div class="col-md-3"><span>{{storeDetails.app_key}}</span>
          </div>
          <div class="col-md-12">
            <label for="api_secret">
              API Secret
              <span class="red">*</span>
            </label>
            <input
              :id="'api_secret'"
              name="api_secret"
              v-model="storeDetails.api_secret"
              v-validate="'required'"
              type="text"
              class="form-control"
              maxlength="8"
            />
            <span v-show="errors.has('api_secret')" class="text-danger">{{
              errors.first("api_secret")
            }}</span>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- API Secret Edit Modal Start -->
  </div>
</template>
<script>
import moment from "moment";
import api from "@/api/apikeys.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import Constants from "@/common/constant.js";
export default {
  mixins: [validationMixin],
  data() {
    return {
      modalTitle: "",
      storeDetails:{},
      allStoreModel: {},
      headerTextVariant: "light",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      showReloadBtn:false,
      constants: Constants,
    };
  },
  created() {
    this.editStoreApiKeys();
    this.copyStoreApiSecret();
    this.copyAppKey();
    this.viewStoreApiSecret();
  },
  methods: {
    generateReport(){
      var self = this;
      self.loading = true;
      var request = {
        is_export: true
      };
      api
        .getMerchantStoreApiKeys(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") +
              "_StoreApiKeys.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    loadDT: function () {
      var self = this;
      $("#storesTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [5] },
          { className: "dt-left break-all", targets: [0, 1, 2, 3, 4] },
        ],
        order: [[5, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Stores Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: localStorage.getItem("token"),
          },
          url: process.env.VUE_APP_API_URL + "/merchantadmin/getmerchantstoreapikeys",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allStoreModel = result.data;
            return self.allStoreModel;
          },
          error: function (request) {
            if (
              request.responseJSON.code == 401 &&
              request.responseJSON.data != null
            ) {
              localStorage.setItem("token", request.responseJSON.data);
              self.loadDT();
            }else{
              error(Constants.datatable_error);
              $('#storesTable_processing').hide();
              self.showReloadBtn = true;
            }
          },
        },
        columns: [
          { data: "merchant_id"},
          { data: "store_id" },
          { data: "retailer" },
          { data: "integrator_id" },
          { data: "category_code" },
          {
            render: function (data, type, full, meta) {
              return (
                full.app_key+'&nbsp;&nbsp;<b-button data-user-id="' + full.id + '" class="copyAppKey custom-edit-btn" title="Copy APP Key"  variant="outline-success"><i class="nav-icon fas fa-copy"></i></b-button>'
              );
            },
          },
          {
            render: function (data, type, full, meta) {
              return (
                '<span id="' + full.id + '">' + full.api_secret.replace(full.api_secret.substring(0, 5),'*****') + '</span>&nbsp;&nbsp;<b-button data-user-id="' + 
                full.id + 
                '" class="copyStoreApiSecret custom-edit-btn" title="Copy API Secret"  variant="outline-success"><i class="nav-icon fas fa-copy"></i></b-button>'+
                '&nbsp;&nbsp;<b-button data-user-id="' + 
                full.id + 
                '" class="viewStoreApiSecret custom-edit-btn" title="View API Secret" variant="outline-success"><i class="nav-icon fas fa-eye"></i></b-button>'
              );
            },
          },
          {
            render: function (data, type, full, meta) {
              return (
                '<b-button data-user-id="' +
                full.id +
                '" class="editStoreApiKeys custom-edit-btn" title="Edit Store API Secret" variant="outline-success"><i class="nav-icon fas fa-edit"></i></b-button>'
              );
            },
          },
        ],
      });

      $("#storesTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#storesTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
    resetModal() {
      var self = this;
      self.storeDetails = {};
    },
    editStoreApiKeys() {
      var self = this;
      $(document).on("click", ".editStoreApiKeys", function (e) {
        self.storeDetails = self.allStoreModel.find(
          (p) => p.id == $(e.currentTarget).attr("data-user-id")
        );
        self.modalTitle = "Edit API Secret";
        self.$bvModal.show("editModal");
      });
    },
    copyStoreApiSecret(){
      var self = this;
      $(document).on("click", ".copyStoreApiSecret", function (e) {
        self.storeDetails = self.allStoreModel.find(
          (p) => p.id == $(e.currentTarget).attr("data-user-id")
        );
        navigator.clipboard.writeText(self.storeDetails.api_secret);
        success(Constants.api_secret_copy_succes);
      });
    },
    copyAppKey(){
      var self = this;
      $(document).on("click", ".copyAppKey", function (e) {
        self.storeDetails = self.allStoreModel.find(
          (p) => p.id == $(e.currentTarget).attr("data-user-id")
        );
        navigator.clipboard.writeText(self.storeDetails.app_key);
        success(Constants.app_key_copy_success);
      });
    },
    viewStoreApiSecret(){
      var self = this;
      $(document).on("click", ".viewStoreApiSecret", function (e) {
        self.storeDetails = self.allStoreModel.find(
          (p) => p.id == $(e.currentTarget).attr("data-user-id")
        );
        $("#"+self.storeDetails.id).html(self.storeDetails.api_secret);
      });
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      this.$validator.validateAll().then((result) => {
        if (result) {
        api
        .updateStoreDetails(self.storeDetails)
        .then((response) => {
          if (response.code == 200) {
            success(response.message);
            $("#storesTable").DataTable().ajax.reload(null, false);
            self.$bvModal.hide("editModal");
            self.resetModal();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
      }
      });
    },
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Store API Keys";
  },
};
</script>

