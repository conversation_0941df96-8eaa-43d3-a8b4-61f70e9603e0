#!/bin/bash

# <PERSON>ript to simplify the release flow.
# 1) Fetch the current release version
# 2) Increase the version (major, minor, patch)
# 3) Add a new git tag
# 4) Push the tag

red=`tput setaf 1`
green=`tput setaf 2`
yellow=`tput setaf 3`
cyan=`tput setaf 6`
strong=`tput smso`
reset=`tput sgr0`

# Parse command line options.
while getopts ":Mmpd" Option
do
  case $Option in
    M ) major=true;;
    m ) minor=true;;
    p ) patch=true;;
  esac
done

shift $(($OPTIND - 1))

# Display usage
if [ -z $major ] && [ -z $minor ] && [ -z $patch ];
then
  echo "usage: $(basename $0) [Mmp] [message]"
  echo ""
  echo "  -M for a major release"
  echo "  -m for a minor release"
  echo "  -p for a patch release"
  echo ""
  echo " Example: release -p \"Some fix\""
  echo " means create a patch release with the message \"Some fix\""
  exit 1
fi

# 1) Fetch the current release version

echo "Fetch remote tags..."

git fetch --prune --tags

version=$(git describe --abbrev=0 --tags)

version=${version:1} # Remove the v in the tag v0.37.10 for example

echo "Current version: ${cyan}v$version${reset}"

# 2) Increase version number

# Build array from version string.

a=( ${version//./ } )

# Increment version numbers as requested.

if [ ! -z $major ]
then
  ((a[0]++))
  a[1]=0
  a[2]=0
fi

if [ ! -z $minor ]
then
  ((a[1]++))
  a[2]=0
fi

if [ ! -z $patch ]
then
  ((a[2]++))
fi

next_version="${a[0]}.${a[1]}.${a[2]}"

username=$(git config user.name)
msg="$1 by $username"

echo "Next Version: ${cyan}${strong}v$next_version${reset}"
echo "Tag Message: ${cyan}$msg${reset}"
echo "${cyan}ChangeLog is going to look like:${reset}"
echo
git log v$version..HEAD --no-merges |egrep '~ ' 

echo "${green}------------------------------------------------------------------------------${reset}"
echo
read -p "Press ${green}Y${reset} to continue with the Release, any other key to quit: " -n 1 -r
echo   # (optional) move to a new line
if [[ $REPLY =~ ^[Y]$ ]]
then
  # If a command fails, exit the script
  set -e

  # Prepare the ChangeLog
  currentDate=`date +"%Y-%m-%d "` 
  echo "Preparing ChangeLog and Release Tag..."
  mv CHANGELOG CHANGELOG.tmp
  echo "# Release v$next_version on $currentDate" > CHANGELOG
  echo >> CHANGELOG
  git log v$version..HEAD --no-merges |egrep '~ ' >> CHANGELOG
  cat CHANGELOG.tmp >> CHANGELOG
  rm -f CHANGELOG.tmp

  # Add ChangeLog and release_tag
  echo "Add ChangeLog and Release Tag..."
  git add CHANGELOG

  git commit -m "- Updating CHANGELOG and Tag info"

  # Add git tag
  echo "Add git tag v$next_version with message: $msg"
  git tag -a "v$next_version" -m "$msg"

  # Push the new tag

  echo "Push the branch & tag"
  git push --tags origin HEAD

  echo "${green}Release Done! Latest version is v$next_version${reset}"
else
  echo "${yellow}Release aborted on user input! You have to manually discard changes in CHANGELOG and inc/tag.php files.${reset}"
fi
