import { loadProgressBar } from 'axios-progress-bar'
import { reward_wheel_axios } from '../main'

const purchaseGiftCards = (request) => {
    loadProgressBar({}, reward_wheel_axios)
    return new Promise((res, rej) => {
        reward_wheel_axios.defaults.headers.common["Authorization"] = 'Bearer ' + localStorage.getItem("token");
        reward_wheel_axios.post('/api/merchantadmin/purchasegiftcards', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getGiftCards = (request) => {
    loadProgressBar({}, reward_wheel_axios)
    return new Promise((res, rej) => {
        reward_wheel_axios.defaults.headers.common["Authorization"] = 'Bearer ' + localStorage.getItem("token");
        reward_wheel_axios.post('/api/merchantadmin/getgiftcards', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const shareGiftCard = (request) => {
    loadProgressBar({}, reward_wheel_axios)
    return new Promise((res, rej) => {
        reward_wheel_axios.defaults.headers.common["Authorization"] = 'Bearer ' + localStorage.getItem("token");
        reward_wheel_axios.post('/api/merchantadmin/sharegiftcard', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getGiftCardDetails = (request) => {
    loadProgressBar({}, reward_wheel_axios)
    return new Promise((res, rej) => {
        reward_wheel_axios.defaults.headers.common["Authorization"] = 'Bearer ' + localStorage.getItem("token");
        reward_wheel_axios.post('/api/merchantadmin/getgiftcarddetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    purchaseGiftCards,
    getGiftCards,
    shareGiftCard,
    getGiftCardDetails
};
