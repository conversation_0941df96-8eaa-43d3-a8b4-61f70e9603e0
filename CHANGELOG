# Release v7.4.1 on 2025-07-22 

    ~ Enhancement: NPM has been updated
# Release v7.4.0 on 2025-05-28 

    ~ Enhancement: Add tip on transaction success and transaction history pages.
# Release v7.3.11 on 2025-05-26 

    ~ Enhancement: NPM has been updated
# Release v7.3.10 on 2025-05-20 

    ~ Enhancement: NPM has been updated
# Release v7.3.9 on 2025-04-28 

    ~ Enhancement: NPM has been updated
# Release v7.3.8 on 2025-04-21 

    ~ Enhancement: NPM has been updated
# Release v7.3.7 on 2025-04-14 

    ~ Enhancement: NPM has been updated
# Release v7.3.6 on 2025-04-07 

    ~ Enhancement: NPM has been updated
# Release v7.3.5 on 2025-04-02 

    ~ Fix: Ensure transaction ranges start at 1 and have no gaps in sequence
# Release v7.3.4 on 2025-03-24 

    ~ Enhancement: NPM has been updated
# Release v7.3.3 on 2025-03-18 

    ~ Enhancement: Regular Updates option added to merge request template
# Release v7.3.2 on 2025-03-04 

    ~ Enhancement: NPM has been updated
# Release v7.3.1 on 2025-02-24 

    ~ Enhancement: NPM has been updated
# Release v7.3.0 on 2025-02-19 

    ~ Fix: Adjust text position for Daily Transaction Email checkbox
# Release v7.2.0 on 2025-02-11 

    ~ Fix: Destroy Click Events in User Edit
# Release v7.1.1 on 2025-02-10 

    ~ Enhancement: NPM has been updated
# Release v7.1.0 on 2025-02-06 

    ~ Fix: Modify transaction by click on link
    ~ Enhancement: Merchant Points Program Report
# Release v7.0.0 on 2025-01-28 

    ~ Enhancement: Delivery fee implemented for Remotepay Transactions
# Release v6.1.2 on 2025-01-20 

    ~ Enhancement: NPM has been updated
# Release v6.1.1 on 2025-01-14 

    ~ Enhancement: NPM has been updated
# Release v6.1.0 on 2025-01-08 

    ~ Enhancement: Centrailizing generate and export for settlement report
# Release v6.0.1 on 2025-01-06 

    ~ Enhancement: NPM has been updated
# Release v6.0.0 on 2024-12-26 

    ~ Enhancement: Learn Brand Implementaion
# Release v5.3.3 on 2024-12-17 

    ~ Enhancement: NPM has been updated
# Release v5.3.2 on 2024-12-10 

    ~ Enhancement: NPM has been updated
# Release v5.3.1 on 2024-12-02 

    ~ Enhancement: NPM has been updated
# Release v5.3.0 on 2024-11-18 

    ~ Fix: Replaced old favicon with a new one
# Release v5.2.4 on 2024-11-11 

    ~ Enhancement: NPM has been updated
# Release v5.2.3 on 2024-11-04 

    ~ Enhancement: NPM has been updated
# Release v5.2.2 on 2024-10-29 

    ~ Enhancement: NPM has been updated
# Release v5.2.1 on 2024-10-21 

    ~ Enhancement: NPM has been updated
# Release v5.2.0 on 2024-10-16 

    ~ Enhancement: Added discounted Fees Report
    ~ Enhancement: Added Modal for Default Merchant Points Program Master
# Release v5.1.2 on 2024-10-07 

    ~ Enhancement: NPM has been updated
# Release v5.1.1 on 2024-10-01 

    ~ Enhancement: NPM has been updated
# Release v5.1.0 on 2024-09-24 

    ~ Fix: Merchant Points Feedback
# Release v5.0.3 on 2024-09-23 

    ~ Enhancement: NPM has been updated
# Release v5.0.2 on 2024-09-17 

    ~ Enhancement: NPM has been updated
# Release v5.0.1 on 2024-09-11 

    ~ Fix: Points program report hide.
# Release v5.0.0 on 2024-09-11 

    ~ Enhancement: Merchant Points Program module integrated
# Release v4.2.5 on 2024-09-10 

    ~ Enhancement: NPM has been updated
# Release v4.2.4 on 2024-09-03 

    ~ Enhancement: NPM has been updated
# Release v4.2.3 on 2024-08-28 

    ~ Fix: Fix the script for aws secret manager
# Release v4.2.2 on 2024-08-27 

    ~ Fix: Adding condition for secret fetch only in Production and staging environment
# Release v4.2.1 on 2024-08-26 

    ~ Fix: Load .env.local first
# Release v4.2.0 on 2024-08-26 

    ~ Enhancement: fetch-secrets.js script to update .env.local without overwriting comments
# Release v4.1.8 on 2024-08-20 

    ~ Enhancement: NPM has been updated
# Release v4.1.7 on 2024-08-13 

    ~ Enhancement: NPM has been updated
# Release v4.1.6 on 2024-07-22 

    ~ Enhancement: NPM has been updated
# Release v4.1.5 on 2024-07-15 

    ~ Enhancement: NPM has been updated
# Release v4.1.4 on 2024-06-25 

    ~ Enhancement: NPM has been updated
# Release v4.1.3 on 2024-06-18 

    ~ Enhancement: NPM has been updated
# Release v4.1.2 on 2024-06-11 

    ~ Enhancement: NPM has been updated
# Release v4.1.1 on 2024-06-04 

    ~ Enhancement: NPM has been updated
# Release v4.1.0 on 2024-05-29 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Added MR template
# Release v4.0.3 on 2024-05-16 

    ~ Fix: The remote pay advance search by Settlement Date or Transaction Date
# Release v4.0.2 on 2024-05-15 

    ~ Fix: The remote pay advance search page data is retrieved based on the scheduled posting date.
# Release v4.0.1 on 2024-04-22 

    ~ Enhancement: NPM has been updated
# Release v4.0.0 on 2024-04-14 

    ~ Enhancement: Sposor implementation
# Release v3.8.19 on 2024-04-08 

    ~ Enhancement: NPM has been updated
# Release v3.8.18 on 2024-03-18 

    ~ Enhancement: NPM has been updated
# Release v3.8.17 on 2024-03-05 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Readme preapred as per proposed format
# Release v3.8.16 on 2024-02-26 

    ~ Enhancement: NPM has been updated
# Release v3.8.15 on 2024-01-30 

    ~ Enhancement: NPM has been updated
# Release v3.8.14 on 2023-11-06 

    ~ Enhancement: NPM has been updated
    ~ Enahncement:readme file updated
# Release v3.8.13 on 2023-09-25 

    ~ Enhancement: NPM has been updated
# Release v3.8.12 on 2023-09-18 

    ~ Enhancement: NPM has been updated
# Release v3.8.11 on 2023-09-11 

    ~ Enhancement: NPM has been updated
# Release v3.8.10 on 2023-09-04 

    ~ Enhancement: NPM has been updated
# Release v3.8.9 on 2023-08-23 

    ~ Fix: The Remote Pay Transaction History page shows the total settled transactions and the sum of the transaction amount
    ~ Fix: Reset the Regional Manager Quick Access PIN, Void PIN
# Release v3.8.8 on 2023-08-18 

    ~ Fix: Remote Pay accepted transaction status shown as settled
# Release v3.8.7 on 2023-08-16 

    ~ Enhancement: NPM has been updated
# Release v3.8.6 on 2023-08-07 

    ~ Enhancement: NPM has been updated
# Release v3.8.5 on 2023-07-24 

    ~ Enhancement: NPM has been updated
# Release v3.8.4 on 2023-07-03 

    ~ Enhancement: Report Performance of export all store
# Release v3.8.3 on 2023-06-27 

    ~ Enhancement: NPM has been updated
# Release v3.8.2 on 2023-06-19 

    ~ Enhancement: NPM has been updated
# Release v3.8.1 on 2023-06-14 

    ~ Fix: Five digit remotepay manager PIN validation
# Release v3.8.0 on 2023-06-13 

    ~ Enhancement: Merchant Login with PIN
# Release v3.7.0 on 2023-06-07 

    ~ Fix: Advance search column hide issue and datatable refresh issue
    ~ Enhancement: Show user role permission in users section
# Release v3.6.0 on 2023-06-05 

    ~ Enhancement: Direct RemotePay transaction modification time show status
    ~ Enhancement: Add RemotePay Manager Role
    ~ Enhancement: Show refresh button on RemotePay Transaction History page
    ~ Enhancement: ReomotePay transactions history in a single component and some fixes in merchant dashboard
# Release v3.5.5 on 2023-06-01 

    ~ Enhancement: Export all store transaction report
# Release v3.5.4 on 2023-05-25 

    ~ Fix: Hide Action Buttons In Advance Search When Expiration Date is Greater Than Current Dare
# Release v3.5.3 on 2023-05-25 

    ~ Fix: Hide Modify, Void and Accept Payment button for expiration
# Release v3.5.2 on 2023-05-24 

    ~ Enhancement: Remote Pay Dashboard Changes
# Release v3.5.1 on 2023-05-22 

    ~ Enhancement: NPM has been updated
# Release v3.5.0 on 2023-05-15 

    ~ Fix: Ecommerce Dashboard text change
    ~ Enhancement: Transaction Modification and Void and Accept Payment Global Options in Ecommerce Dashboard
# Release v3.4.2 on 2023-05-01 

    ~ Enhancement: NPM has been updated
# Release v3.4.1 on 2023-04-26 

    ~ Fix: Transaction modification text change and select modification type change select to radio button
# Release v3.4.0 on 2023-04-25 

    ~ Fix: Store API keys page text change
# Release v3.3.2 on 2023-04-04 

    ~ Enhancement: Transaction modification by clicking on particular link
# Release v3.3.1 on 2023-04-03 

    ~ Enhancement: NPM has been updated
# Release v3.3.0 on 2023-04-03 

    ~ Fix: Store timing will be added for the eCommerce store only.
# Release v3.2.3 on 2023-03-27 

    ~ Enhancement: NPM has been updated
# Release v3.2.2 on 2023-03-20 

    ~ Enhancement: NPM has been updated
# Release v3.2.1 on 2023-03-13 

    ~ Enhancement: NPM has been updated
# Release v3.2.0 on 2023-03-06 

    ~ Enhancement: NPM has been updated
# Release v3.1.6 on 2023-02-27 

    ~ Enhancement: NPM has been updated
# Release v3.1.5 on 2023-01-23 

    ~ Enhancement: NPM has been updated
# Release v3.1.4 on 2023-01-10 

    ~ Fix: Pos Transactions Void option
# Release v3.1.3 on 2023-01-03 

    ~ Enhancement: NPM has been updated
# Release v3.1.2 on 2022-12-20 

    ~ Enhancement: NPM has been updated
# Release v3.1.1 on 2022-12-06 

    ~ Enhancement: NPM has been updated
# Release v3.1.0 on 2022-12-05 

    ~ Enhancement: Storewise current month's fees for enabled corporate parent
# Release v3.0.5 on 2022-11-15 

    ~ Enhancement: NPM has been updated
# Release v3.0.4 on 2022-11-07 

    ~ Enhancement: NPM has been updated
# Release v3.0.3 on 2022-10-01 

    ~ Enhancement: Transaction Report and Settlement Report modified according to ecommerce transactions
# Release v3.0.2 on 2022-09-29 

    ~ Fix: Void transaction page sorting with date
# Release v3.0.1 on 2022-09-28 

    ~ Fix:  Remove minimum one day validation, when merchant select date
# Release v3.0.0 on 2022-09-27 

    ~ Enhancement :  Merchant Admin Ecommerce Transaction And Ecommerce Transaction Modification
# Release v2.13.0 on 2022-09-20 

    ~ Enhancement: NPM has been updated
# Release v2.12.1 on 2022-08-29 

    ~ Enahncement: NPM has been updated
# Release v2.12.0 on 2022-08-17 

    ~ Enhancement: Update user role from Corporate Parent login
    ~ Enhancement : Introduce a new role like Regional Manager
# Release v2.11.4 on 2022-08-16 

    ~ Enhancement: NPM has been updated
# Release v2.11.3 on 2022-08-08 

    ~ Enhancement: NPM has been updated
# Release v2.11.2 on 2022-08-01 

    ~ Enhancement: NPM has been updated
# Release v2.11.1 on 2022-07-26 

    ~ Enhancement: NPM has been updated
# Release v2.11.0 on 2022-07-20 

    ~ Enhancement: Store drop search added with min 3 chars in all the report pages
    ~ Enhancement: Store dropdown for reports reverted
# Release v2.10.0 on 2022-07-19 

    Revert "Revert "~ Enhancement: Store dropdown for reports reverted""
    Revert "~ Enhancement: Store dropdown for reports reverted"
    Revert "~ Bug Fix: API refresh token fixed for response type BLOB"
    Revert "~ Enhancement: Store drop search added with min 3 chars in all the report pages"
    ~ Enhancement: NPM has been updated
    ~ Enhancement: Store dropdown for reports reverted
# Release v2.9.1 on 2022-07-18 

    ~ Bug Fix: API refresh token fixed for response type BLOB
# Release v2.9.0 on 2022-07-18 

    ~ Enhancement: Store drop search added with min 3 chars in all the report pages
# Release v2.8.4 on 2022-07-12 

    ~ Bug Fix: Restriction removed from Store Manager for Void Transaction menu
# Release v2.8.3 on 2022-07-11 

    ~ Enhancement: NPM has been updated
# Release v2.8.2 on 2022-07-06 

    ~ Bug Fix: Void menu access restricted from Accountant and store manager
# Release v2.8.1 on 2022-07-05 

    ~ Enhancement: Npm has been updated
# Release v2.8.0 on 2022-06-27 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Forgot password send email validation added
# Release v2.7.4 on 2022-06-20 

    ~ Enhancement: npm has been updated
# Release v2.7.3 on 2022-06-14 

    ~ Enhancement: Void Transaction menu search modified
# Release v2.7.2 on 2022-06-13 

    ~ Enhancement: Npm has been updated on 10-06-2022
# Release v2.7.1 on 2022-05-16 

    ~ Enhancement: NPM has been updated.
# Release v2.7.0 on 2022-05-10 

    ~ Fix: Merchant name removed form Transaction history and Employee ID added in Transaction report
# Release v2.6.0 on 2022-04-25 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Void Transaction report added
    ~ Enhancement: Void transaction menu show/hide based on checkbox is selected against CP in the Admin Panel
# Release v2.5.1 on 2022-04-04 

    ~ Enhancement: NPM has been updated
# Release v2.5.0 on 2022-03-28 

    ~ Enhancement: NPM has been updated
# Release v2.4.1 on 2022-01-18 

    ~ Enhancement: Merchant Release popup designed as per the new requirement.
# Release v2.4.0 on 2022-01-12 

    ~ Enhancement: Historical Transaction Settlement Report Created
# Release v2.3.3 on 2021-12-17 

    ~ BugFix : In merchent admin panel unable to generate transaction report fixed
# Release v2.3.2 on 2021-12-15 

    ~ Enhancement : Generate Multi Location reports for Merchant implementation done
# Release v2.3.1 on 2021-12-14 

    ~ Bug Fix: Message shown when there is no release
# Release v2.3.0 on 2021-12-14 

    ~ Enhancement: Showing release note on Merchant Admin login.
    ~ Bugfix : Adding timeouts to merchant login bug fixed
    Revert "~ Enhancement : In Merchant Admin panel Corporate parent   upgrade user role if wanted implementation done"
    ~ Enhancement : In Merchant Admin panel Corporate parent   upgrade user role if wanted implementation done
# Release v2.2.8 on 2021-12-09 

    ~ Fix: JWT algo change issue fixed by loggin out the user
# Release v2.2.7 on 2021-12-07 

    ~ Enhancement : Merchant Admins would like an option in the User profile to be able to initiate a password reset email implementation done
    ~ Enhancement : All selector option when adding stores to a User implementation done
    ~ Enhancement : From the dashboard, merchant would like to drill through and see store level activity by clicking on that store implementation done
    ~ Bugfix : Fix the number of statuses that show in the User setup for Merchant Admins fixed
# Release v2.2.6 on 2021-11-22 

    ~ Enhancement : Adding timeouts to merchant login implementation done
# Release v2.2.5 on 2021-11-19 

    ~ Enhancement : In Merchant Admin Being able to sort by any/all columns in the dashboard implementation done
# Release v2.2.4 on 2021-11-16 

    ~ Bug Fix: Default page set to Dashboard
    ~ Bug fix: Remove the js and css version from css and js files
# Release v2.2.3 on 2021-09-13 

    ~ Bug Fix: Transaction Report fix for different terminal transactions coming under one
# Release v2.2.2 on 2021-08-13 

    ~ Enhancement: Export option added in Merchant Dashboard
    ~ Enhancement: Datatable search disabled until user types three characters
    ~ Enhancement: Add Reload button if error occurs in datatable
# Release v2.2.1 on 2021-08-06 

    ~ Bug Fix: Datatable alert suppressed with error message
# Release v2.2.0 on 2021-07-27 

    ~ Enhancement: Add Export All Stores button in the Settlement/Fees Report
# Release v2.1.0 on 2021-07-07 

    ~ Enhancement: Store Managers can login ingto the Merchant Admin Panel with their credentials and view reports
# Release v2.0.5 on 2021-07-06 

    ~ Bug Fix: Daily Transaction Email menu and Store menu removed from Accountant panel
# Release v2.0.4 on 2021-07-05 

    ~ Enhancement: Store name shown instead of merchant name in all reports
    ~ Enhancement: Receive Daily Transaction Mail option added in Store Manager and Accountant Creation
# Release v2.0.3 on 2021-07-03 

    ~ Enhancement : Device Manager Creation: Password needs to be sent over email implemented
    ~ Enhancement: A help text shown on transaction report based on end date
# Release v2.0.2 on 2021-07-01 

    ~ Enhancement : Marchent Dashboard Page's Average   transaction over last 7 days implementation done
# Release v2.0.1 on 2021-06-29 

    ~ Enhancement: Merchant Dashboard implemented
# Release v2.0.0 on 2021-06-25 

    ~ Enhancement: Changes in Transaction Report and Settlement report
    ~ Bug Fixes : Merchant Transaction report total amount calculation fixed
    ~ Enhancement :  Implement accountant role for merchant done
    ~ Enhancement: Merchant Location Transaction Report add accordian
# Release v1.15.0 on 2021-05-28 

    ~ Enhancement: Changes in the Settlement/Fees report
    ~ Enhancement: Merchant Location Transaction report show/hide Voids checkbox replaced by Switch
    ~ Bug Fixes: All report related bug fixed
    ~ Enhancement: Field added in Edit Profile section for maximum 5 Daily Transaction Activity Emails
# Release v1.14.1 on 2021-05-20 

    ~ Bug Fixes: Device manager listing screen infinite loading issue resolved
# Release v1.14.0 on 2021-04-16 

    ~ Bug Fixes: All report related bug fixed
# Release v1.13.0 on 2021-03-22 

    ~ Enhancement: Corporate Parent Login with Primary Contact Email done and Profile section modified as per Primary Contact
# Release v1.12.0 on 2021-02-18 

    ~ Enhancement: Regional manager option hidden from sidebar
# Release v1.10.0 on 2021-02-02 

    ~ Enhancement: Store Manager Password and Middle name removed
    ~ Enhancement: Forgot password option implemented for all the merchant admin portal users.
    ~ Enhancement: Password added instead of PIN in Device manager creation
    ~ Enhancement: Column manager ID added into the store manager data table.
# Release v1.9.0 on 2020-12-29 

    ~ Enhancement: No follow meta tag has been introduced in the index.html page
    ~ Enhancement: CanPay Tip report V2 (employee) front end UI implemented.
# Release v1.8.0 on 2020-12-04 

    ~ ClientFeedback: Phone number removed from different userlisting. Term Created At changed to Created On.
# Release v1.7.0 on 2020-11-25 

    ~ Enhancement: Tip Report V1 By Shifts UI implemented along with export feature
    ~ ClientFeedback: Merchant bank details cannot be updated from the UI and credentials must be masked.
    ~ Enhancement: Long term settlement report UI implemented with export functionality
    ~ Enhancement: V1 settlement report UI implemented with export functionality
    ~ Enhancement: UI implemented for Merchant Location Trnsaction Report
# Release v1.6.0 on 2020-11-06 

    ~ SecurityIssueFix: Vulnerability with Weak Logout Management fixed.
# Release v1.5.0 on 2020-10-14 

    ~ Enhancement: User and bank status implementation modified according to the status of v1 system
    ~ Enhancement: Regional Manager bank account details update section implemented
    ~ Enhancement: All type users store access details modal UI enhanced.
# Release v1.4.0 on 2020-08-18 

    ~ BugFixes: Transaction cancellation comment related bugs fixed.
    ~ Enhancement: Add reason for cancelling transaction feature implemented
# Release v1.3.0 on 2020-08-05 

    ~ BugFix: Transaction amount fixed to two decimal places for transaction listing
    ~ BugFixes: Transaction listing issues resolved
# Release v1.2.0 on 2020-07-20 

    ~ Bug Fixes: Name validation added and Password changed message modified
    ~ Enhancement: Transaction cancellation feature implemented
    ~ ClientFeedback: Text Canpay changed into "CanPay"
# Release v1.1.0 on 2020-05-22 

    ~ BugFixes: Login API modified so that CP and RM cannot login from POS
# Release v1.0.0 on 2020-05-15 

    ~ BugFixes: Update Profile section invalid dob format issue resolved
    ~ Enhancement: Change password feature implemented datapicker validation added and canpay white logo included
    ~ Enhancement: IT Personnel and Employee CRUD implemented
    ~ Enhancement: Store manager CRUD implemeneted.
    ~ Enhancement: Update regional manager feature implemented
    ~ Enhancement: Regional manager listing implemented.
    ~ Enhancement: Edit User Profile feature implemented.
    ~ Enhancement: scss structure optimized. Canpay colors added.
    ~ Bug Fixes: Issue with data toggle dropdown resolved
    ~ Enhancement: Theme along with login API implemented.
    ~ Enhancement: ENV files added and readme updated accordingly
