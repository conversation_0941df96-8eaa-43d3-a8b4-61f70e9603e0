<template>
  <div>
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0 text-dark">Gift Cards</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item">
                <router-link to="/dashboard">Home</router-link>
              </li>
              <li class="breadcrumb-item active">Gift Cards</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <section class="content">
    <div class="container-fluid">
      <!-- Purchase Gift Cards Form -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Purchase Gift Cards</h3>
        </div>
        <div class="card-body">
          <form ref="giftCardForm" @submit.stop.prevent="purchaseGiftCards" class="needs-validation">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="quantity">
                    Quantity
                    <span class="red">*</span>
                  </label>
                  <input
                    name="quantity"
                    v-validate="'required|integer|min_value:1|max_value:100'"
                    type="number"
                    v-model="giftCardDetails.quantity"
                    class="form-control"
                    placeholder="Enter quantity (1-100)"
                    min="1"
                    max="100"
                  />
                  <span v-show="errors.has('quantity')" class="text-danger">{{
                    errors.first("quantity")
                  }}</span>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="amount">
                    Amount per Gift Card ($)
                    <span class="red">*</span>
                  </label>
                  <input
                    name="amount"
                    v-validate="'required|decimal:2|min_value:10|max_value:250'"
                    type="number"
                    step="0.01"
                    v-model="giftCardDetails.amount"
                    class="form-control"
                    placeholder="Enter amount ($10 - $250)"
                    min="10"
                    max="250"
                  />
                  <span v-show="errors.has('amount')" class="text-danger">{{
                    errors.first("amount")
                  }}</span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label for="notes">Notes (Optional)</label>
                  <textarea
                    name="notes"
                    v-model="giftCardDetails.notes"
                    class="form-control"
                    rows="3"
                    placeholder="Add any notes for this gift card purchase..."
                  ></textarea>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <button
                  type="submit"
                  class="btn btn-success"
                  :disabled="isSubmitting"
                >
                  <i class="fas fa-shopping-cart" v-if="!isSubmitting"></i>
                  <i class="fas fa-spinner fa-spin" v-else></i>
                  {{ isSubmitting ? 'Processing...' : 'Purchase Gift Cards' }}
                </button>
                <button
                  type="button"
                  class="btn btn-secondary ml-2"
                  @click="resetForm"
                >
                  <i class="fas fa-undo"></i>
                  Reset
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      <!-- Gift Cards Table -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Your Gift Cards</h3>
        </div>
        <div class="card-body">
          <table id="giftCardsTable" class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>Gift Card Code</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Purchase Date</th>
                <th>Shared With</th>
                <th>Actions</th>
              </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>

    <!-- Share Gift Card Modal -->
    <b-modal
      id="share-modal"
      ref="share-modal"
      :header-text-variant="headerTextVariant"
      title="Share Gift Card"
      ok-title="Share"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleShareOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
      @hidden="resetShareModal"
    >
      <form ref="shareForm">
        <div class="form-group">
          <label for="phone">
            Consumer Phone Number
            <span class="red">*</span>
          </label>
          <input
            type="tel"
            class="form-control"
            v-model="shareDetails.phone"
            name="phone"
            v-validate="'required|digits:10'"
            placeholder="Enter 10-digit phone number"
            maxlength="10"
          />
          <span v-show="errors.has('phone')" class="text-danger">{{
            errors.first("phone")
          }}</span>
          <small class="form-text text-muted">
            The gift card will be sent to this phone number via SMS.
          </small>
        </div>
      </form>
    </b-modal>
    </section>
  </div>
</template>

<script>
import api from "@/api/giftcard.js";
import Constants from "@/common/constant.js";

export default {
  data() {
    return {
      giftCardDetails: {
        quantity: '',
        amount: '',
        notes: ''
      },
      shareDetails: {
        phone: '',
        giftCardId: null
      },
      isSubmitting: false,
      headerTextVariant: "light",
      constants: Constants,
    };
  },
  methods: {
    purchaseGiftCards() {
      var self = this;
      self.$validator.validateAll().then((result) => {
        if (result) {
          self.isSubmitting = true;
          
          const request = {
            quantity: parseInt(self.giftCardDetails.quantity),
            amount: parseFloat(self.giftCardDetails.amount),
            notes: self.giftCardDetails.notes || ''
          };

          api
            .purchaseGiftCards(request)
            .then((response) => {
              if (response.code == 200) {
                success(response.message || "Gift cards purchased successfully!");
                self.resetForm();
                self.loadGiftCardsTable();
              } else {
                error(response.message || "Failed to purchase gift cards");
              }
            })
            .catch((err) => {
              error(err.response && err.response.data && err.response.data.message ? err.response.data.message : "An error occurred while purchasing gift cards");
            })
            .finally(() => {
              self.isSubmitting = false;
            });
        }
      });
    },

    resetForm() {
      this.giftCardDetails = {
        quantity: '',
        amount: '',
        notes: ''
      };
      this.$validator.reset();
    },

    openShareModal(giftCardId) {
      this.shareDetails.giftCardId = giftCardId;
      this.$bvModal.show("share-modal");
    },

    resetShareModal() {
      this.shareDetails = {
        phone: '',
        giftCardId: null
      };
      this.$validator.reset();
    },

    handleShareOk(bvModalEvt) {
      var self = this;
      bvModalEvt.preventDefault();
      
      self.$validator.validateAll('shareForm').then((result) => {
        if (result) {
          self.shareGiftCard();
        }
      });
    },

    shareGiftCard() {
      var self = this;
      
      const request = {
        gift_card_id: self.shareDetails.giftCardId,
        phone: self.shareDetails.phone
      };

      api
        .shareGiftCard(request)
        .then((response) => {
          if (response.code == 200) {
            success(response.message || "Gift card shared successfully!");
            self.$bvModal.hide("share-modal");
            self.loadGiftCardsTable();
          } else {
            error(response.message || "Failed to share gift card");
          }
        })
        .catch((err) => {
          error(err.response && err.response.data && err.response.data.message ? err.response.data.message : "An error occurred while sharing the gift card");
        });
    },

    loadGiftCardsTable() {
      var self = this;
      $("#giftCardsTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [5] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4] },
          { className: "dt-center", targets: [5] },
        ],
        order: [[3, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Gift Cards Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          url: process.env.VUE_APP_API_URL + "/api/merchantadmin/getgiftcards",
          type: "POST",
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          error: function (xhr, error, thrown) {
            error(Constants.datatable_error);
          },
        },
        columns: [
          { data: "gift_card_code" },
          { 
            data: "amount",
            render: function (data, type, full, meta) {
              return "$" + parseFloat(data).toFixed(2);
            }
          },
          { data: "status" },
          { data: "purchase_date" },
          { data: "shared_with" },
          {
            render: function (data, type, full, meta) {
              if (full.status === 'Active' && !full.shared_with) {
                return (
                  '<button data-gift-card-id="' +
                  full.id +
                  '" class="shareGiftCard btn btn-success btn-sm" title="Share Gift Card"><i class="fas fa-share"></i> Share</button>'
                );
              } else {
                return '<span class="text-muted">-</span>';
              }
            },
          },
        ],
      });

      // Handle share button clicks
      $(document).on("click", ".shareGiftCard", function (e) {
        const giftCardId = $(e.currentTarget).attr("data-gift-card-id");
        self.openShareModal(giftCardId);
      });

      $("#giftCardsTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });
    },
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadGiftCardsTable();
    }, 500);
    document.title = "CanPay - Gift Cards";
  },
};
</script>

<style scoped>
.red {
  color: red;
}
</style>
