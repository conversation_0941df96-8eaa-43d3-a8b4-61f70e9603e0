server {
     	server_name demo-merchant.canpaydebit.com;
    	root   /opt/cp-dev3-merchant/www/dist;

        index index.html index.htm;

		listen 80;
		listen 443 ssl;

		if ($scheme = http) {
		   return 301 https://$server_name$request_uri;
		}

		gzip on;
		gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
		gzip_proxied no-cache no-store private expired auth;
		gzip_min_length 1000;
				add_header 'Access-Control-Allow-Credentials' 'true';
				add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';

		ssl_protocols TLSv1.2;

    ssl_certificate /opt/certs/cp-dev3-merchant/rh-cert.pem;
    ssl_certificate_key /opt/certs/cp-dev3-merchant/rh-key.pem;

    access_log /opt/cp-dev3-merchant/logs/access.log;
    error_log /opt/cp-dev3-merchant/logs/error.log;

    location / {
        try_files $uri $uri/ @rewrites;
    }

    location @rewrites {
        rewrite ^(.+)$ /index.html last;
    }
}
