//
body {
    font-size: 0.93rem !important;
}

// Datatables CSS Start
th.dt-center,
td.dt-center {
    text-align: center;
}

.dataTables_empty {
    text-align: center !important;
}

div.dataTables_wrapper div.dataTables_processing {
    background: none !important;
    border: none !important;
    color: $cp-primary !important;
    box-shadow: none !important;
}

.page-item.active .page-link {
    background-color: $cp-primary !important;
    border-color: $cp-primary !important;
}

// Datatables CSS End
// Top Progress Bar CSS Start
#nprogress .bar {
    background: $cp-primary !important;
    z-index: 999999 !important;
}

#nprogress .peg {
    box-shadow: 0 0 10px $cp-primary, 0 0 5px $cp-primary !important;
    z-index: 999999 !important;
}

#nprogress .spinner-icon {
    border-top-color: $cp-primary !important;
    border-left-color: $cp-primary !important;
    z-index: 999999 !important;
}

// Top Progress Bar CSS End
// Scroll Style Start
.scroll-style-3::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: $cp-white;
}

.scroll-style-3::-webkit-scrollbar {
    width: 6px;
    background-color: $cp-white;
}

.scroll-style-3::-webkit-scrollbar-thumb {
    background-color: $cp-primary !important;
}

// Scroll Style End
.modal-content {
    width: 750px !important;
    left: -20% !important;
}

.red {
    color: $cp-red !important;
}

.white {
    color: $cp-white!important;
}

.custom-edit-btn {
    cursor: pointer !important;
    color: $cp-primary !important;
}

.router-link-active {
    background: $cp-primary !important;
    color: $cp-white !important;
}

.brand-link.router-link-active {
    background: none !important;
}

.multiselect__tag {
    background: $cp-primary !important;
}

.multiselect__option--highlight {
    background: $cp-primary !important;
}

.brand-link .brand-image {
    margin-left: 0 !important;
}

.card-success:not(.card-outline)>.card-header {
    background-color: $cp-primary !important;
}

.badge-success {
    background-color: $cp-primary !important;
}

.btn-outline-success:hover {
    color: $cp-white !important;
    background-color: $cp-primary !important;
}

.btn-outline-success {
    color: $cp-primary !important;
    border-color: $cp-primary !important;
}

.btn-success {
    background-color: $cp-primary !important;
    border-color: $cp-primary !important;
}

.modal-header {
    background-color: $cp-primary !important;
}

.datepicker table tr td.active:active,
.datepicker table tr td.active.highlighted:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active.highlighted.active {
    background-color: $cp-primary !important;
    background-image: none !important;
}

.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active[disabled],
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active.disabled:hover[disabled] {
    background-color: $cp-primary !important;
    background-image: none !important;
}


/**
* For toggle switch 
*/

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 25px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 21px;
    width: 21px;
    left: 10px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked+.slider {
    background-color: $cp-primary;
}

input:focus+.slider {
    box-shadow: 0 0 1px $cp-primary;
}

input:checked+.slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}


/* Rounded sliders */

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.cp-green {
    color: $cp-primary;
}

.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
    background-color: $cp-primary !important;
    background-image: none !important;
    color: $cp-white;
}

// for the store access modal
.odd-row {
    background-color: #f2f2f2 !important;
    margin-left: 20px !important;
    margin-right: 20px !important;
}

.even-row {
    margin-left: 20px !important;
    margin-right: 20px !important;
}

.row-value {
    margin-top: 10px;
    margin-bottom: 10px;
}

.f-13 {
    font-size: 13px;
}

.ml-10 {
    margin-left: 10px;
}

input[type="checkbox"] {
    background-color: $cp-primary !important;
}

.custom-checkbox .custom-control-input:checked~.custom-control-label::before {
    background-color: $cp-primary !important;
    border-color: $cp-primary !important;
}

input::placeholder {
    color: #adadad !important;
    display: inline-block !important;
    margin-bottom: 10px !important;
    padding-top: 2px !important;
}

.modal .modal-huge {
    max-width: 90% !important;
    width: 90% !important;
    ;
}

.text-gray {
    color: #949191;
}

.loading {
    position: fixed;
    z-index: 999;
    overflow: show;
    margin: auto;
    top: 0;
    left: 150px;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 50px;
}


/* Transparent Overlay */

.loading:before {
    content: '';
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
}

// .b-right{
//     border-right: 2px solid $cp-black !important;
// }
// .b-left{
//     border-left: 2px solid $cp-black !important;
// }
// .b-top{
//     border-top: 2px solid $cp-black !important;
// }
// .b-bottom{
//     border-bottom: 2px solid $cp-black !important;
// }
.th-transparent {
    background-color: transparent !important;
    // border-bottom: 2px solid $cp-black !important;
}

.cursor-pointer {
    cursor: pointer;
    color: #337ab7 !important;
}

.report-total-cls {
    background-color: #cccccc;
}

.old-sales-act-cls {
    background-color: #f1efef;
}

.new-sales-act-cls {
    background-color: #dee0de;
}

.b-table-details td {
    border: 1px solid #f00;
    border-radius: 5px;
    padding: 10px !important;
}

.merchantLocationTableCls {
    margin-bottom: 0px !important;
}

.mb-10 {
    margin-bottom: 10px !important;
}

.text-gray-italic {
    color: #949191;
    font-style: italic;
    text-align: center;
}

.th-white {
    background-color: #ffffff !important;
}

.table-bordered td,
.table-bordered th {
    border: 1px solid #c1c0c0 !important;
}

.start-date,
.end-date {
    caret-color: transparent !important;
}

.calendar-link {
    color: $cp-blue;
}

.select-date-cls {
    margin-top: 5px;
    margin-right: 10px;
}

.helper-text-right {
    float: right;
    color: $cp-red;
}

.checkbox-modal-class {
    margin-top: 2.5rem;
}

.dailyTransactionEmail{
    position:relative;
    top:-2px;
}

.export-api-btn {
    float: right;
}

.releasetagcls {
    font-size: 1rem !important;
    text-align: left;
    margin-top: 5px;
}

.releasenotecls {
    font-size: 1rem !important;
    text-align: left !important;
}

.swal2-title {
    line-height: .5 !important;
    text-align: left !important;
}

.todo-list>li {
    padding-top: 6px !important;
    background: #e8f9ee;
    border-left: 2px solid #149240;
    color: #0a0a0a;
    margin-bottom: 10px !important;
}

.release-note-description {
    margin-bottom: 0px !important;
    font-size: 15px;
    font-style: italic;
    color: #646363;
}

.swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen)>.swal2-modal {
    width: max-content;
}

.direct-chat-name {
    margin-bottom: 0px !important;
}

.text-green {
    color: rgb(48 161 27);
    font-weight: bold;
}

.text-red {
    color: rgb(243 9 9);
    font-weight: bold;
}

.text-black {
    color: #212529;
    font-weight: normal;
}

.td-pad-15 td {
    padding: 16px !important;
}

button .forgetpassword-ok-label {
    cursor: pointer;
}