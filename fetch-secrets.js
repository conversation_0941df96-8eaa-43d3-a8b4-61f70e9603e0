const fs = require('fs');
const path = require('path');
const { SecretsManagerClient, GetSecretValueCommand } = require('@aws-sdk/client-secrets-manager');

// Determine the path to the .env.local file
const envLocalPath = path.resolve(__dirname, '.env.local');
let envPath;

// Check if .env.local exists, otherwise use .env
if (fs.existsSync(envLocalPath)) {
    envPath = envLocalPath;
} else {
    envPath = path.resolve(__dirname, '.env');
}

// Load environment variables from the determined .env file
require('dotenv').config({ path: envPath });

// Log environment variables for verification
console.log('Using environment variables from:', envPath);
console.log('AWS_REGION:', process.env.AWS_REGION);
console.log('AWS_SECRET_NAME:', process.env.AWS_SECRET_NAME);

// Explicitly set the credentials
const client = new SecretsManagerClient({
    region: process.env.AWS_REGION,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
});

const secretName = process.env.AWS_SECRET_NAME;

async function fetchSecrets() {
    try {
        // Fetch secrets from AWS Secrets Manager
        const command = new GetSecretValueCommand({ SecretId: secretName });
        const data = await client.send(command);
        const secrets = JSON.parse(data.SecretString);

        // Path to the .env.local file
        const envFilePath = path.resolve(__dirname, '.env.local');

        // Read existing .env.local content
        let existingContent = '';
        if (fs.existsSync(envFilePath)) {
            existingContent = fs.readFileSync(envFilePath, 'utf8');
        }

        // Process existing content into a map
        const existingVars = {};
        existingContent.split('\n').forEach(line => {
            const [key, value] = line.split('=');
            if (key && value) {
                existingVars[key.trim()] = line.trim();
            } else if (line.trim().startsWith('#')) {
                existingVars[line.trim()] = line.trim();
            }
        });

        // Merge new secrets with existing environment variables
        Object.keys(secrets).forEach(key => {
            existingVars[key] = `${key}=${secrets[key]}`;
        });

        // Create the content for .env.local preserving comments
        const updatedContent = Object.values(existingVars).join('\n');

        // Write updated content to .env.local
        fs.writeFileSync(envFilePath, updatedContent, 'utf8');
        console.log('.env.local file updated with secrets from AWS Secrets Manager');
    } catch (error) {
        console.error('Error fetching secrets from AWS Secrets Manager:', error);
    }
}

fetchSecrets();
