<template>
  <div>
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Tip Configuration</h1>
          </div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <b-button
                variant="outline-success"
                style="margin-top: -48px; float: right;"
                @click="openModal('add')"
              >
                <i class="fas fa-plus"></i> Add Tip Configuration
              </b-button>
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Tip Configurations</h3>
                  <b-button
                    class="btn-danger export-api-btn"
                    @click="reloadDatatable"
                    v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <div class="card-body">
                  <table
                    id="tipConfigTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Store</th>
                        <th>Tip Options</th>
                        <th>Status</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Tip Config Modal Start -->
    <b-modal
      id="tip-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="tipConfigForm" @submit.stop.prevent="save" class="needs-validation">
        <div class="card">
          <div class="card-body row">
            <div class="col-md-12" v-if="add && showStoreSpecificOption">
              <div class="form-group">
                <label for="isStoreSpecific">Configuration Type</label>
                <div>
                  <label class="switch">
                    <input type="checkbox" id="isStoreSpecific" name="isStoreSpecific" 
                           v-model="isStoreSpecific" true-value="1" false-value="0">
                    <span class="slider round"></span>
                  </label>
                  <label for="isStoreSpecific" class="ml-2">Store Specific Configuration</label>
                  <small class="form-text text-muted">
                    If enabled, this configuration will override the global configuration for this store.
                  </small>
                </div>
              </div>
            </div>
            <div class="col-md-12" v-if="add">
              <div class="form-group" v-if="isStoreSpecific == '1'">
                <label for="store">Store<span class="red">*</span></label>
                <multiselect
                  v-model="selectedStore"
                  :options="storeList"
                  :custom-label="customStoreSelectLabel"
                  placeholder="Select Store"
                  label="retailer"
                  track-by="id"
                  :searchable="true"
                  :allow-empty="false"
                  :show-labels="false"
                  name="store"
                  v-validate="'required'"
                ></multiselect>
                <span v-show="errors.has('store')" class="text-danger">Store is required</span>
              </div>
            </div>
            <div class="col-md-12" v-else>
              <div class="form-group" v-if="!tipConfigDetails.is_parent">
                <label for="store">Store</label>
                <input
                  type="text"
                  class="form-control"
                  v-model="tipConfigDetails.retailer"
                  disabled
                />
              </div>
              <div class="form-group" v-else>
                <label for="store">Global Configuration</label>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <label>Tip Options</label>
                <div v-for="(option, index) in tipConfigDetails.tip_options" :key="index" class="d-flex mb-2">
                  <div class="input-group">
                    <input
                      type="text"
                      class="form-control"
                      v-model="option.percentage"
                      :name="`tip_option_${index}`"
                      @input="formatPercentage('tip_options', index, 'percentage'); validateTipOptions()"
                      placeholder="Percentage"
                      :class="{'is-invalid': index > 0 && parseFloat(option.percentage) <= parseFloat(tipConfigDetails.tip_options[index-1].percentage)}"
                    />
                    <div class="input-group-append">
                      <span class="input-group-text">%</span>
                    </div>
                    <b-button variant="danger" @click="deleteOption(index)" size="sm" class="ml-2">
                      <i class="fas fa-trash"></i>
                    </b-button>
                    <div v-if="index > 0 && parseFloat(option.percentage) <= parseFloat(tipConfigDetails.tip_options[index-1].percentage)" class="invalid-feedback">
                      Option {{index+1}} must be greater than option {{index}}
                    </div>
                  </div>
                </div>
                <b-button 
                  variant="outline-primary" 
                  @click="addOption" 
                  size="sm" 
                  class="mt-2"
                  v-if="tipConfigDetails.tip_options.length < 3"
                >
                  <i class="fas fa-plus"></i> Add Option
                </b-button>
                <small class="form-text text-muted">Add tip percentage options that will be shown to customers</small>
                <small class="form-text text-muted">*** <strong>Consumers can skip tipping by selecting the 'No Tip' option.</strong></small>
              </div>
            </div>

          </div>
        </div>
      </form>
    </b-modal>

    <!-- API Key Modal -->
    <b-modal
      id="api-key-modal"
      ref="api-key-modal"
      title="Store API Keys"
      :header-text-variant="headerTextVariant"
      hide-footer
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Retailer</th>
                  <th>APP Key</th>
                  <th>API Secret</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in apiKeyData" :key="index">
                  <td>{{ item.retailer }}</td>
                  <td>
                    {{ item.app_key }}
                    <button class="btn btn-sm btn-outline-success ml-1" @click="copyToClipboard(item.app_key)" title="Copy APP Key">
                      <i class="fas fa-copy"></i>
                    </button>
                  </td>
                  <td>
                    <span :id="'secret_'+index">{{ item.api_secret.replace(item.api_secret.substring(0, 5), '*****') }}</span>
                    <button class="btn btn-sm btn-outline-success ml-1" @click="copyToClipboard(item.api_secret)" title="Copy API Secret">
                      <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success ml-1" @click="viewSecret(index, item.api_secret)" title="View API Secret">
                      <i class="fas fa-eye"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="card-footer text-right">
          <b-button variant="secondary" @click="$bvModal.hide('api-key-modal')">Close</b-button>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import api from "@/api/tip.js";
import user_api from "@/api/user.js";
import Constants from "@/common/constant.js";
import Multiselect from "vue-multiselect";

export default {
  name: "TipConfiguration",
  components: {
    Multiselect
  },
  data() {
    return {
      headerTextVariant: "white",
      modalTitle: "",
      tipConfigDetails: {
        id: null,
        store_id: null,
        default_tip_percentage: "",
        status_id: 1,
        tip_options: []
      },
      selectedStore: null,
      storeList: [],
      apiKeyData: [],
      activeStatusID: 1,
      inactiveStatusID: 0,
      allTipConfigModel: [],
      showReloadBtn: false,
      showStoreSpecificOption: false,
      isStoreSpecific: 0,
      add: true,
      type: "",
      edit_id: null,
      user: JSON.parse(localStorage.getItem("store_user")),
      constants: Constants
    };
  },
  created() {
    var self = this;
    self.editTipConfig();
    self.deleteTipConfig();
    self.viewAppKeySecret();
  },
  methods: {
    reloadDatatable() {
      var self = this;
      self.loadDT();
    },
    customStoreSelectLabel(store) {
      return store && store.retailer ? store.retailer + ' - ' + store.state : '';
    },
    openModal(type) {
      var self = this;
      self.getStores();
      self.type = type;
      if (type == "edit") {
        self.modalTitle = "Edit Tip Configuration";
        self.add = false;
        self.$bvModal.show("tip-modal");
      } else {
        self.add = true;
        self.modalTitle = "Add Tip Configuration";
        // Reset Form Fields
        self.tipConfigDetails = {
          id: null,
          store_id: null,
          default_tip_percentage: "",
          status_id: self.activeStatusID,
          tip_options: [{ percentage: "" }]
        };
        self.$bvModal.show("tip-modal");
      }
    },
    resetModal() {
      var self = this;
      self.tipConfigDetails = {
        id: null,
        store_id: null,
        tip_options: [{ percentage: "" }]
      };
      self.selectedStore = null;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    addOption() {
      var self = this;
      if (self.tipConfigDetails.tip_options.length < 3) {
        self.tipConfigDetails.tip_options.push({ percentage: "" });
      } else {
        error("Maximum of 3 tip options allowed");
      }
    },
    deleteOption(index) {
      var self = this;
      if (self.tipConfigDetails.tip_options.length > 1) {
        self.tipConfigDetails.tip_options.splice(index, 1);
      } else {
        error("At least one tip option is required");
      }
    },
    formatPercentage(field, index, subfield) {
      let value;
      if (subfield) {
        value = this.tipConfigDetails[field][index][subfield];
        let formattedValue = value.replace(/[^0-9.]/g, '');
        
        let parts = formattedValue.split('.');
        if (parts.length > 2) {
          formattedValue = parts[0] + '.' + parts.slice(1).join('');
        }
        
        if (parts[1]) {
          formattedValue = parts[0] + '.' + (parts[1].substring(0, 2) || '');
        }
        
        // Ensure value doesn't exceed 100
        if (parseFloat(formattedValue) > 100) {
          formattedValue = "100";
        }
        
        this.tipConfigDetails[field][index][subfield] = formattedValue;
      } else {
        value = this.tipConfigDetails[field];
        let formattedValue = value.replace(/[^0-9.]/g, '');
        
        let parts = formattedValue.split('.');
        if (parts.length > 2) {
          formattedValue = parts[0] + '.' + parts.slice(1).join('');
        }
        
        if (parts[1]) {
          formattedValue = parts[0] + '.' + (parts[1].substring(0, 2) || '');
        }
        
        // Ensure value doesn't exceed 100
        if (parseFloat(formattedValue) > 100) {
          formattedValue = "100";
        }
        
        this.tipConfigDetails[field] = formattedValue;
      }
    },
    getStores() {
      var self = this;
      self.loading = true;
      let request = {
        params: {
          is_tip_configured: 1
        }
      };
      user_api
        .getStores(request)
        .then((response) => {
          if ((response.code == 200)) {
            self.storeList = response.data;
          } else {
            error(response.message);
          }
          self.loading = false;
        })
        .catch((err) => {
          self.loading = false;
          error(err.response.data.message);
        });
    },
    addTipConfiguration() {
      var self = this;
      // Prepare request data
      var request = {
        is_store_specific: self.isStoreSpecific,
        store_id: self.isStoreSpecific == '1' ? self.selectedStore.id : null,
        tip_options: self.tipConfigDetails.tip_options.map(option => option.percentage).filter(Boolean)
      };
      
      // Call API to save the details
      api
        .saveTipConfiguration(request)
        .then((response) => {
          if (response.code == 200) {
            success(response.message);
            $("#tipConfigTable").DataTable().ajax.reload(null, false);
            self.$bvModal.hide("tip-modal");
            self.resetModal();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    editTipConfiguration() {
      var self = this;
      // Prepare request data
      var request = {
        id: self.tipConfigDetails.id,
        tip_options: self.tipConfigDetails.tip_options.map(option => option.percentage).filter(Boolean)
      };
      
      // Call API to save the details
      api
        .updateTipConfiguration(request)
        .then((response) => {
          if (response.code == 200) {
            success(response.message);
            $("#tipConfigTable").DataTable().ajax.reload(null, false);
            self.$bvModal.hide("tip-modal");
            self.resetModal();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    save() {
      var self = this;
      // Check if at least one tip option has a value
      let hasValidTipOption = self.tipConfigDetails.tip_options.some(option => 
        option.percentage && option.percentage.trim() !== ''
      );
      
      if (!hasValidTipOption) {
        error("At least one tip option is required");
        return;
      }
      
      // Check if percentages are in ascending order
      if (!self.validateTipOptions()) {
        error("Tip percentages must be in ascending order");
        return;
      }
      
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          if (self.add) {
            self.addTipConfiguration();
          } else {
            self.editTipConfiguration();
          }
        }
      });
    },
    validateTipOptions() {
      var self = this;
      let isValid = true;
      
      // Check if options are in ascending order
      if (self.tipConfigDetails.tip_options.length > 1) {
        for (let i = 1; i < self.tipConfigDetails.tip_options.length; i++) {
          const prevValue = parseFloat(self.tipConfigDetails.tip_options[i-1].percentage);
          const currValue = parseFloat(self.tipConfigDetails.tip_options[i].percentage);
          
          if (!isNaN(prevValue) && !isNaN(currValue) && currValue <= prevValue) {
            isValid = false;
            break;
          }
        }
      }
      
      return isValid;
    },
    editTipConfig() {
      var self = this;
      $(document).on("click", ".editTipConfig", function (e) {
        self.tipConfigDetails = self.allTipConfigModel.find(
          (p) => p.id == $(e.currentTarget).attr("data-id")
        );
        
        // Convert string tip options to array of objects
        if (typeof self.tipConfigDetails.tip_options === 'string') {
          self.tipConfigDetails.tip_options = self.tipConfigDetails.tip_options.split(',')
            .map(option => ({ percentage: option.trim() }));
        } else if (Array.isArray(self.tipConfigDetails.tip_options) && typeof self.tipConfigDetails.tip_options[0] !== 'object') {
          self.tipConfigDetails.tip_options = self.tipConfigDetails.tip_options
            .map(option => ({ percentage: option }));
        }
        
        // Ensure at least one tip option
        if (!self.tipConfigDetails.tip_options || self.tipConfigDetails.tip_options.length === 0) {
          self.tipConfigDetails.tip_options = [{ percentage: "" }];
        }
        console.log(self.tipConfigDetails);
        
        self.modalTitle = "Edit Tip Configuration";
        self.add = false;
        self.$bvModal.show("tip-modal");
      });
    },
    deleteTipConfig() {
      var self = this;
      $(document).on("click", ".deleteTipConfig", function (e) {
        if (confirm("Are you sure you want to delete this tip configuration?")) {
          var id = $(e.currentTarget).attr("data-id");
          api
            .deleteTipConfiguration(id)
            .then((response) => {
              if (response.code == 200) {
                success(response.message);
                $("#tipConfigTable").DataTable().ajax.reload(null, false);
              } else {
                error(response.message);
              }
            })
            .catch((err) => {
              error(err.response.data.message);
            });
        }
      });
    },
    viewAppKeySecret() {
      var self = this;

      $(document).on("click", ".viewAppKeySecret", function (e) {
        var id = $(e.currentTarget).attr("data-id");

        api.getStoreApiKeys({ store_id: id })
          .then((response) => {
            if (response.code === 200) {
              self.apiKeyData = response.data;
              self.$bvModal.show("api-key-modal");
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err.response.data.message);
          });
      });
    },
    loadDT: function () {
      var self = this;
      
      // Prepare request data based on user role
      let requestData = { _token: "{{csrf_token()}}" };
      
      if (self.user.role_name === self.constants.role_corporate_parent) {
        requestData.corporate_parent_id = self.user.user_id;
      }
      
      $("#tipConfigTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [1, 2, 3] },
          { className: "dt-left break-all", targets: [0, 1] },
          { className: "dt-center", targets: [2, 3] }
        ],
        order: [[0, "asc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Tip Configurations Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>'
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_"
        },
        ajax: {
          headers: {
            Authorization: localStorage.getItem("token")
          },
          url: process.env.VUE_APP_API_URL + "/merchantadmin/tipconfigurations",
          type: "POST",
          data: requestData,
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allTipConfigModel = result.data;
            result.cpConfig ? self.showStoreSpecificOption = false : self.showStoreSpecificOption = true;
            result.cpConfig ? self.isStoreSpecific = true : self.isStoreSpecific = false;
            return self.allTipConfigModel;
          },
          error: function (request) {
            if (
              request.responseJSON.code == 401 &&
              request.responseJSON.data != null
            ) {
              localStorage.setItem("token", request.responseJSON.data);
              self.loadDT();
            } else {
              error(Constants.datatable_error);
              $('#tipConfigTable_processing').hide();
              self.showReloadBtn = true;
            }
          }
        },
        columns: [
          { data: "retailer" },
          { 
            render: function (data, type, full, meta) {
              if (!full.tip_options) return "";
              
              let options = Array.isArray(full.tip_options) 
                ? full.tip_options 
                : full.tip_options.split(',').map(o => o.trim());
                
              return options.join("%, ") + "%";
            }
          },
          {
            render: function (data, type, full, meta) {
              var checked = full.is_enabled == self.activeStatusID ? "checked" : "";
              return (
                '<label class="switch"><input class="statusChangeTipConfig" ' +
                checked +
                ' type="checkbox" data-id="' +
                full.id +
                '"><span class="slider round"></span></label>'
              );
            }
          },
          {
            render: function (data, type, full, meta) {
              if (full.is_parent) {
                return (
                  '<b-button data-id="' +
                  full.id +
                  '" class="editTipConfig custom-edit-btn mr-2" title="Edit"><i class="fas fa-edit"></i></b-button>'
                );
              } else {
               return (
                  '<b-button data-id="' +
                  full.id +
                  '" class="editTipConfig custom-edit-btn mr-2" title="Edit"><i class="fas fa-edit"></i></b-button>' +
                  '<b-button data-id="' +
                  full.id +
                  '" class="deleteTipConfig custom-edit-btn mr-2" title="Delete"><i class="fas fa-trash"></i></b-button>' +
                  '<b-button data-id="' +
                  full.store_id +
                  '" class="viewAppKeySecret custom-edit-btn" title="View App Key & Secret"><i class="fas fa-eye"></i></b-button>'
                );

              }
            }
          }
        ]
      });

      // Handle status change
      $(document).on("change", ".statusChangeTipConfig", function () {
        var id = $(this).attr("data-id");
        var is_enabled = $(this).prop("checked") ? self.activeStatusID : self.inactiveStatusID;
        
        api
          .updateTipConfigurationStatus({ id: id, is_enabled: is_enabled })
          .then((response) => {
            if (response.code == 200) {
              success(response.message);
            } else {
              error(response.message);
              $("#tipConfigTable").DataTable().ajax.reload(null, false);
            }
          })
          .catch((err) => {
            error(err.response.data.message);
            $("#tipConfigTable").DataTable().ajax.reload(null, false);
          });
      });
    },
    copyToClipboard(text) {
      navigator.clipboard.writeText(text);
      success(this.constants.api_secret_copy_succes || "Copied to clipboard successfully!");
    },
    viewSecret(index, secret) {
      document.getElementById('secret_'+index).textContent = secret;
    }
  },
  beforeDestroy(){
    $(document).off('click', '.statusChangeTipConfig');
    $(document).off('click', '.editTipConfig');
    $(document).off('click', '.deleteTipConfig');
    $(document).off('click', '.viewAppKeySecret');
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Tip Configuration";
  }
};
</script>

<style scoped>
.red {
  color: red;
}
.export-api-btn {
  float: right;
  margin-top: -5px;
}
</style>




