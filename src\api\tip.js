const saveTipConfiguration = (request) => {
    return new Promise((res, rej) => {
        axios.post('/merchantadmin/createtipconfiguration', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const updateTipConfiguration = (request) => {
    return new Promise((res, rej) => {
        axios.post('/merchantadmin/updatetipconfiguration', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateTipConfigurationStatus = (request) => {
    return new Promise((res, rej) => {
        axios.post('/merchantadmin/updatetipconfigstatus', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const deleteTipConfiguration = (id) => {
    return new Promise((res, rej) => {
        axios.post('/merchantadmin/deletetipconfiguration', { id: id })
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getStoreApiKeys = (request) => {
    return new Promise((res, rej) => {
        axios.post('/merchantadmin/getstoreapikeys', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
export default {
    saveTipConfiguration,
    updateTipConfiguration,
    deleteTipConfiguration,
    updateTipConfigurationStatus,
    getStoreApiKeys
};