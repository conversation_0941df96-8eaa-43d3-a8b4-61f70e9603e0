{"name": "merchant-web-app", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "watch": "vue-cli-service build --watch", "fetch-secrets": "node fetch-secrets.js"}, "dependencies": {"@aws-sdk/client-secrets-manager": "3.721.0", "@gtm-support/vue2-gtm": "^1.3.0", "axios": "^0.26.1", "axios-progress-bar": "^1.2.0", "bootstrap": "^4.6.2", "bootstrap-vue": "^2.23.1", "chartist": "0.11.0", "dotenv": "^16.6.1", "file-saver": "^2.0.5", "jquery": "^3.7.1", "node-sass": "^4.14.1", "nprogress": "^0.2.0", "popper.js": "^1.16.1", "register-service-worker": "^1.7.2", "sass-loader": "8.0.2", "sweetalert2": "^9.17.4", "vee-validate": "^2.2.15", "vue": "^2.7.16", "vue-axios": "^2.1.5", "vue-chartist": "2.1.2", "vue-firestore": "^0.3.30", "vue-loading-spinner": "^1.0.11", "vue-meta": "1.5.2", "vue-moment": "^4.1.0", "vue-multiselect": "^2.1.9", "vue-router": "^3.6.5", "vue-search-select": "^2.9.6", "vue-sweetalert2": "^5.0.11", "vuejs-countdown-timer": "^2.1.3", "vuelidate": "^0.7.7", "vuetify": "1.4.3"}, "devDependencies": {"@vue/cli-plugin-router": "^4.5.19", "@vue/cli-service": "^4.5.19", "sass": "^1.89.2", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.7.16"}, "browserslist": ["> 1%", "last 2 versions"]}