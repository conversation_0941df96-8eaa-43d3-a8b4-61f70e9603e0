#!/bin/bash
echo "======================================="
echo "Setting up PORTAL....."
echo "======================================="

# Load environment variables from .env.local
export $(grep -v '^#' .env.local | xargs)

# Strip quotes from APP_ENV, if any
APP_ENV=$(echo "$APP_ENV" | sed 's/^"\(.*\)"$/\1/')

# Install global dependencies
npm install

if [ "$NODE_ENV" = "production" ] || [ "$NODE_ENV" = "staging" ]; then
    # Fetch secrets from AWS Secrets Manager
    npm run fetch-secrets
fi

# Build the project
npm run build

echo "======================================="
echo "PORTAL set up successfully"
echo "======================================="
